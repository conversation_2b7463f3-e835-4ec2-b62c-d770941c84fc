"""
Wikipedia client for Klavis AI MCP server.
Handles Wikipedia API interactions and content retrieval.
"""

import logging
import requests
import wikipediaapi
from typing import Dict, List, Optional, Any
from urllib.parse import quote

logger = logging.getLogger(__name__)


class KlavisWikipediaClient:
    """Wikipedia client for Klavis AI MCP server."""
    
    def __init__(self, language: str = "en", country: Optional[str] = None, enable_cache: bool = False):
        """Initialize the Wikipedia client.
        
        Args:
            language: Wikipedia language code (e.g., 'en', 'es', 'fr')
            country: Country code for localized content
            enable_cache: Whether to enable caching (not implemented yet)
        """
        self.language = language
        self.country = country
        self.enable_cache = enable_cache
        self.user_agent = "KlavisAI-WikipediaMCP/1.0.0 (https://klavis.ai)"
        
        # Parse language and variant
        self.base_language, self.language_variant = self._parse_language_variant(language)
        
        # Initialize Wikipedia API client
        self.wiki = wikipediaapi.Wikipedia(
            user_agent=self.user_agent,
            language=self.base_language,
            extract_format=wikipediaapi.ExtractFormat.WIKI
        )
        self.api_url = f"https://{self.base_language}.wikipedia.org/w/api.php"
    
    def _parse_language_variant(self, language: str) -> tuple[str, Optional[str]]:
        """Parse language code and variant (e.g., 'zh-cn' -> ('zh', 'cn'))."""
        if '-' in language:
            parts = language.split('-', 1)
            return parts[0], parts[1]
        return language, None
    
    def _add_variant_to_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Add language variant to API parameters if needed."""
        if self.language_variant:
            params['variant'] = self.language_variant
        return params
    
    def search(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search Wikipedia for articles matching a query.
        
        Args:
            query: Search query string
            limit: Maximum number of results to return
            
        Returns:
            List of search results with title, snippet, and metadata
        """
        logger.info(f"Searching Wikipedia for: {query} (limit: {limit})")
        
        params = {
            'action': 'query',
            'format': 'json',
            'list': 'search',
            'srsearch': query,
            'srlimit': min(limit, 50),  # Wikipedia API limit
            'srprop': 'snippet|titlesnippet|size|wordcount|timestamp'
        }
        
        # Add variant parameter if needed
        params = self._add_variant_to_params(params)
        
        try:
            response = requests.get(self.api_url, params=params)
            response.raise_for_status()
            data = response.json()
            
            results = []
            for item in data.get('query', {}).get('search', []):
                results.append({
                    'title': item.get('title', ''),
                    'snippet': item.get('snippet', ''),
                    'pageid': item.get('pageid', 0),
                    'wordcount': item.get('wordcount', 0),
                    'timestamp': item.get('timestamp', '')
                })
            
            return results
        except Exception as e:
            logger.error(f"Error searching Wikipedia: {e}")
            return []
    
    def get_article(self, title: str) -> Dict[str, Any]:
        """Get the full content of a Wikipedia article.
        
        Args:
            title: The title of the Wikipedia article
            
        Returns:
            Dictionary containing the article information
        """
        logger.info(f"Getting Wikipedia article: {title}")
        
        try:
            page = self.wiki.page(title)
            
            if not page.exists():
                return {
                    'title': title,
                    'exists': False,
                    'error': 'Page does not exist'
                }
            
            # Get sections
            sections = self._extract_sections(page.sections)
            
            # Get categories (limited to avoid too much data)
            categories = list(page.categories.keys())[:50]
            
            # Get links (limited to avoid too much data)
            links = list(page.links.keys())[:100]
            
            return {
                'title': page.title,
                'pageid': page.pageid,
                'summary': page.summary,
                'text': page.text,
                'url': page.fullurl,
                'sections': sections,
                'categories': categories,
                'links': links,
                'exists': True
            }
        except Exception as e:
            logger.error(f"Error getting Wikipedia article: {e}")
            return {
                'title': title,
                'exists': False,
                'error': str(e)
            }
    
    def get_summary(self, title: str) -> str:
        """Get a summary of a Wikipedia article.
        
        Args:
            title: The title of the Wikipedia article
            
        Returns:
            The article summary
        """
        logger.info(f"Getting Wikipedia summary: {title}")
        
        try:
            page = self.wiki.page(title)
            
            if not page.exists():
                return f"No Wikipedia article found for '{title}'."
            
            return page.summary
        except Exception as e:
            logger.error(f"Error getting Wikipedia summary: {e}")
            return f"Error retrieving summary for '{title}': {str(e)}"
    
    def get_sections(self, title: str) -> List[Dict[str, Any]]:
        """Get the sections of a Wikipedia article.
        
        Args:
            title: The title of the Wikipedia article
            
        Returns:
            List of sections with titles and content
        """
        logger.info(f"Getting Wikipedia sections: {title}")
        
        try:
            page = self.wiki.page(title)
            
            if not page.exists():
                return []
            
            return self._extract_sections(page.sections)
        except Exception as e:
            logger.error(f"Error getting Wikipedia sections: {e}")
            return []
    
    def _extract_sections(self, sections) -> List[Dict[str, Any]]:
        """Extract section information from Wikipedia page sections."""
        result = []
        for section in sections:
            section_data = {
                'title': section.title,
                'text': section.text[:1000] if section.text else '',  # Limit text length
                'level': getattr(section, 'level', 0)
            }
            result.append(section_data)
            
            # Recursively add subsections
            if hasattr(section, 'sections') and section.sections:
                subsections = self._extract_sections(section.sections)
                result.extend(subsections)
        
        return result
    
    def get_links(self, title: str) -> List[str]:
        """Get the links contained within a Wikipedia article.
        
        Args:
            title: The title of the Wikipedia article
            
        Returns:
            List of linked article titles
        """
        logger.info(f"Getting Wikipedia links: {title}")
        
        try:
            page = self.wiki.page(title)
            
            if not page.exists():
                return []
            
            # Return first 100 links to avoid too much data
            return list(page.links.keys())[:100]
        except Exception as e:
            logger.error(f"Error getting Wikipedia links: {e}")
            return []
    
    def get_related_topics(self, title: str, limit: int = 10) -> List[str]:
        """Get topics related to a Wikipedia article based on links and categories.
        
        Args:
            title: The title of the Wikipedia article
            limit: Maximum number of related topics to return
            
        Returns:
            List of related topic titles
        """
        logger.info(f"Getting related topics for: {title}")
        
        try:
            page = self.wiki.page(title)
            
            if not page.exists():
                return []
            
            # Get related topics from links and categories
            related = []
            
            # Add some links as related topics
            links = list(page.links.keys())[:limit//2]
            related.extend(links)
            
            # Add some categories as related topics
            categories = list(page.categories.keys())[:limit//2]
            related.extend(categories)
            
            # Remove duplicates and limit results
            related = list(dict.fromkeys(related))[:limit]
            
            return related
        except Exception as e:
            logger.error(f"Error getting related topics: {e}")
            return []
    
    def summarize_for_query(self, title: str, query: str, max_length: int = 250) -> str:
        """Get a summary of a Wikipedia article tailored to a specific query.

        Args:
            title: The title of the Wikipedia article
            query: The query to focus the summary on
            max_length: The maximum length of the summary

        Returns:
            A query-focused summary
        """
        logger.info(f"Getting query-focused summary for: {title}, query: {query}")

        try:
            page = self.wiki.page(title)
            if not page.exists():
                return f"No Wikipedia article found for '{title}'."

            text_content = page.text
            query_lower = query.lower()
            text_lower = text_content.lower()

            start_index = text_lower.find(query_lower)
            if start_index == -1:
                # If query not found, return the beginning of the summary or article text
                summary_part = page.summary[:max_length]
                if not summary_part:
                    summary_part = text_content[:max_length]
                return summary_part + "..." if len(summary_part) >= max_length else summary_part

            # Try to get context around the query
            context_start = max(0, start_index - (max_length // 2))
            context_end = min(len(text_content), start_index + len(query) + (max_length // 2))

            snippet = text_content[context_start:context_end]

            if len(snippet) > max_length:
                snippet = snippet[:max_length]

            return snippet + "..." if len(snippet) >= max_length or context_end < len(text_content) else snippet

        except Exception as e:
            logger.error(f"Error generating query-focused summary for '{title}': {e}")
            return f"Error generating query-focused summary for '{title}': {str(e)}"

    def summarize_section(self, title: str, section_title: str, max_length: int = 150) -> str:
        """Get a summary of a specific section of a Wikipedia article.

        Args:
            title: The title of the Wikipedia article
            section_title: The title of the section to summarize
            max_length: The maximum length of the summary

        Returns:
            A summary of the specified section
        """
        logger.info(f"Getting summary for section: {section_title} in article: {title}")

        try:
            page = self.wiki.page(title)
            if not page.exists():
                return f"No Wikipedia article found for '{title}'."

            target_section = None

            # Helper function to find the section
            def find_section_recursive(sections_list, target_title):
                for sec in sections_list:
                    if sec.title.lower() == target_title.lower():
                        return sec
                    # Check subsections
                    found_in_subsection = find_section_recursive(sec.sections, target_title)
                    if found_in_subsection:
                        return found_in_subsection
                return None

            target_section = find_section_recursive(page.sections, section_title)

            if not target_section or not target_section.text:
                return f"Section '{section_title}' not found or is empty in article '{title}'."

            summary = target_section.text[:max_length]
            return summary + "..." if len(target_section.text) > max_length else summary

        except Exception as e:
            logger.error(f"Error summarizing section '{section_title}' for article '{title}': {e}")
            return f"Error summarizing section '{section_title}': {str(e)}"

    def extract_facts(self, title: str, topic_within_article: Optional[str] = None, count: int = 5) -> List[str]:
        """Extract key facts from a Wikipedia article.

        Args:
            title: The title of the Wikipedia article
            topic_within_article: Optional topic/section to focus fact extraction
            count: The number of facts to extract

        Returns:
            A list of key facts (strings)
        """
        logger.info(f"Extracting key facts for: {title}, topic: {topic_within_article}")

        try:
            page = self.wiki.page(title)
            if not page.exists():
                return [f"No Wikipedia article found for '{title}'."]

            text_to_process = ""
            if topic_within_article:
                # Try to find the section text
                def find_section_text_recursive(sections_list, target_title):
                    for sec in sections_list:
                        if sec.title.lower() == target_title.lower():
                            return sec.text
                        found_in_subsection = find_section_text_recursive(sec.sections, target_title)
                        if found_in_subsection:
                            return found_in_subsection
                    return None

                section_text = find_section_text_recursive(page.sections, topic_within_article)
                if section_text:
                    text_to_process = section_text
                else:
                    # Fallback to summary if specific topic section not found
                    text_to_process = page.summary
            else:
                text_to_process = page.summary

            if not text_to_process:
                return ["No content found to extract facts from."]

            # Basic sentence splitting
            sentences = [s.strip() for s in text_to_process.split('.') if s.strip()]

            facts = []
            for sentence in sentences[:count]:
                if sentence:  # Ensure not an empty string after strip
                    facts.append(sentence + ".")  # Add back the period

            return facts if facts else ["Could not extract facts from the provided text."]

        except Exception as e:
            logger.error(f"Error extracting key facts for '{title}': {e}")
            return [f"Error extracting key facts for '{title}': {str(e)}"]

    def get_coordinates(self, title: str) -> Dict[str, Any]:
        """Get the coordinates of a Wikipedia article if available.

        Args:
            title: The title of the Wikipedia article

        Returns:
            Dictionary with coordinates or error message
        """
        logger.info(f"Getting coordinates for: {title}")

        try:
            page = self.wiki.page(title)

            if not page.exists():
                return {
                    'title': title,
                    'coordinates': None,
                    'error': 'Page does not exist'
                }

            # Try to get coordinates from page properties
            if hasattr(page, 'coordinates') and page.coordinates:
                return {
                    'title': title,
                    'coordinates': {
                        'lat': page.coordinates[0],
                        'lon': page.coordinates[1]
                    }
                }
            else:
                return {
                    'title': title,
                    'coordinates': None,
                    'message': 'No coordinates available for this article'
                }
        except Exception as e:
            logger.error(f"Error getting coordinates: {e}")
            return {
                'title': title,
                'coordinates': None,
                'error': str(e)
            }
