"""
Wikipedia MCP tools implementation for Klavis AI.
Individual tool functions for Wikipedia operations.
"""

import logging
import json
import sys
import os
from typing import Dict, List, Any, Optional

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from tools.wikipedia_client import KlavisWikipediaClient

logger = logging.getLogger(__name__)

# Global client instance
_wikipedia_client: Optional[KlavisWikipediaClient] = None


def get_wikipedia_client() -> KlavisWikipediaClient:
    """Get or create the Wikipedia client instance."""
    global _wikipedia_client
    if _wikipedia_client is None:
        _wikipedia_client = KlavisWikipediaClient()
    return _wikipedia_client


async def search_wikipedia(query: str, limit: int = 10) -> Dict[str, Any]:
    """Search Wikipedia for articles matching a query.
    
    Args:
        query: Search query string
        limit: Maximum number of results to return (default: 10)
        
    Returns:
        Dictionary with search results
    """
    logger.info(f"Tool: Searching Wikipedia for: {query}")
    
    try:
        client = get_wikipedia_client()
        results = client.search(query, limit=limit)
        
        return {
            "query": query,
            "results": results,
            "count": len(results)
        }
    except Exception as e:
        logger.error(f"Error in search_wikipedia: {e}")
        return {
            "query": query,
            "results": [],
            "count": 0,
            "error": str(e)
        }


async def get_wikipedia_article(title: str) -> Dict[str, Any]:
    """Get the full content of a Wikipedia article.
    
    Args:
        title: The title of the Wikipedia article
        
    Returns:
        Dictionary containing the article information
    """
    logger.info(f"Tool: Getting Wikipedia article: {title}")
    
    try:
        client = get_wikipedia_client()
        article = client.get_article(title)
        return article
    except Exception as e:
        logger.error(f"Error in get_wikipedia_article: {e}")
        return {
            "title": title,
            "exists": False,
            "error": str(e)
        }


async def get_wikipedia_summary(title: str) -> Dict[str, Any]:
    """Get a summary of a Wikipedia article.
    
    Args:
        title: The title of the Wikipedia article
        
    Returns:
        Dictionary with article title and summary
    """
    logger.info(f"Tool: Getting Wikipedia summary: {title}")
    
    try:
        client = get_wikipedia_client()
        summary = client.get_summary(title)
        
        return {
            "title": title,
            "summary": summary
        }
    except Exception as e:
        logger.error(f"Error in get_wikipedia_summary: {e}")
        return {
            "title": title,
            "summary": f"Error retrieving summary: {str(e)}"
        }


async def get_wikipedia_sections(title: str) -> Dict[str, Any]:
    """Get the sections of a Wikipedia article.
    
    Args:
        title: The title of the Wikipedia article
        
    Returns:
        Dictionary with article title and sections
    """
    logger.info(f"Tool: Getting Wikipedia sections: {title}")
    
    try:
        client = get_wikipedia_client()
        sections = client.get_sections(title)
        
        return {
            "title": title,
            "sections": sections,
            "count": len(sections)
        }
    except Exception as e:
        logger.error(f"Error in get_wikipedia_sections: {e}")
        return {
            "title": title,
            "sections": [],
            "count": 0,
            "error": str(e)
        }


async def get_wikipedia_links(title: str) -> Dict[str, Any]:
    """Get the links contained within a Wikipedia article.
    
    Args:
        title: The title of the Wikipedia article
        
    Returns:
        Dictionary with article title and links
    """
    logger.info(f"Tool: Getting Wikipedia links: {title}")
    
    try:
        client = get_wikipedia_client()
        links = client.get_links(title)
        
        return {
            "title": title,
            "links": links,
            "count": len(links)
        }
    except Exception as e:
        logger.error(f"Error in get_wikipedia_links: {e}")
        return {
            "title": title,
            "links": [],
            "count": 0,
            "error": str(e)
        }


async def get_wikipedia_related_topics(title: str, limit: int = 10) -> Dict[str, Any]:
    """Get topics related to a Wikipedia article.
    
    Args:
        title: The title of the Wikipedia article
        limit: Maximum number of related topics to return (default: 10)
        
    Returns:
        Dictionary with article title and related topics
    """
    logger.info(f"Tool: Getting Wikipedia related topics: {title}")
    
    try:
        client = get_wikipedia_client()
        related_topics = client.get_related_topics(title, limit=limit)
        
        return {
            "title": title,
            "related_topics": related_topics,
            "count": len(related_topics)
        }
    except Exception as e:
        logger.error(f"Error in get_wikipedia_related_topics: {e}")
        return {
            "title": title,
            "related_topics": [],
            "count": 0,
            "error": str(e)
        }


async def get_wikipedia_coordinates(title: str) -> Dict[str, Any]:
    """Get the coordinates of a Wikipedia article if available.

    Args:
        title: The title of the Wikipedia article

    Returns:
        Dictionary with coordinates information
    """
    logger.info(f"Tool: Getting Wikipedia coordinates: {title}")

    try:
        client = get_wikipedia_client()
        coordinates = client.get_coordinates(title)
        return coordinates
    except Exception as e:
        logger.error(f"Error in get_wikipedia_coordinates: {e}")
        return {
            "title": title,
            "coordinates": None,
            "error": str(e)
        }


async def wikipedia_summarize_for_query(title: str, query: str, max_length: int = 250) -> Dict[str, Any]:
    """Get a summary of a Wikipedia article tailored to a specific query.

    Args:
        title: The title of the Wikipedia article
        query: The query to focus the summary on
        max_length: The maximum length of the summary (default: 250)

    Returns:
        Dictionary with query-focused summary
    """
    logger.info(f"Tool: Getting query-focused summary for: {title}, query: {query}")

    try:
        client = get_wikipedia_client()
        summary = client.summarize_for_query(title, query, max_length=max_length)

        return {
            "title": title,
            "query": query,
            "summary": summary,
            "max_length": max_length
        }
    except Exception as e:
        logger.error(f"Error in wikipedia_summarize_for_query: {e}")
        return {
            "title": title,
            "query": query,
            "summary": f"Error generating query-focused summary: {str(e)}",
            "max_length": max_length
        }


async def wikipedia_summarize_section(title: str, section_title: str, max_length: int = 150) -> Dict[str, Any]:
    """Get a summary of a specific section of a Wikipedia article.

    Args:
        title: The title of the Wikipedia article
        section_title: The title of the section to summarize
        max_length: The maximum length of the summary (default: 150)

    Returns:
        Dictionary with section summary
    """
    logger.info(f"Tool: Getting summary for section: {section_title} in article: {title}")

    try:
        client = get_wikipedia_client()
        summary = client.summarize_section(title, section_title, max_length=max_length)

        return {
            "title": title,
            "section_title": section_title,
            "summary": summary,
            "max_length": max_length
        }
    except Exception as e:
        logger.error(f"Error in wikipedia_summarize_section: {e}")
        return {
            "title": title,
            "section_title": section_title,
            "summary": f"Error summarizing section: {str(e)}",
            "max_length": max_length
        }


async def wikipedia_extract_facts(title: str, topic_within_article: str = "", count: int = 5) -> Dict[str, Any]:
    """Extract key facts from a Wikipedia article, optionally focused on a topic.

    Args:
        title: The title of the Wikipedia article
        topic_within_article: Optional topic/section to focus fact extraction (default: "")
        count: The number of facts to extract (default: 5)

    Returns:
        Dictionary with extracted facts
    """
    logger.info(f"Tool: Extracting key facts for: {title}, topic: {topic_within_article}")

    try:
        client = get_wikipedia_client()
        # Convert empty string to None for backward compatibility
        topic = topic_within_article if topic_within_article.strip() else None
        facts = client.extract_facts(title, topic, count=count)

        return {
            "title": title,
            "topic_within_article": topic_within_article,
            "facts": facts,
            "count": len(facts)
        }
    except Exception as e:
        logger.error(f"Error in wikipedia_extract_facts: {e}")
        return {
            "title": title,
            "topic_within_article": topic_within_article,
            "facts": [f"Error extracting facts: {str(e)}"],
            "count": 0
        }
